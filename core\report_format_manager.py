#!/usr/bin/env python3
"""
REPORT FORMAT MANAGER
Generates WORD, PDF, and EXCEL reports with proper formatting, fonts, and layout
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import formatting libraries with fallbacks
try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_TABLE_ALIGNMENT
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

class ReportFormatManager:
    """
    Manages report generation in multiple formats with proper styling
    """
    
    def __init__(self, output_dir: str = None):
        self.output_dir = output_dir or './reports'
        self.ensure_output_directory()
        
        # Font specifications from FINAL REPORT requirements
        self.fonts = {
            'word': {
                'body': 'Cambria (Body)',
                'heading': 'Calibri (Headings)',
                'body_size': 14,
                'heading_size': 26
            },
            'pdf': {
                'body': 'Times-Roman',
                'heading': 'Helvetica-Bold',
                'body_size': 12,
                'heading_size': 18
            },
            'excel': {
                'body': 'Calibri',
                'heading': 'Calibri',
                'body_size': 11,
                'heading_size': 14
            }
        }
    
    def ensure_output_directory(self):
        """Ensure output directory exists"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def generate_report(self, report_data: Dict[str, Any], format_type: str, 
                       filename: str = None) -> Dict[str, Any]:
        """
        Generate report in specified format
        
        Args:
            report_data: Report data structure
            format_type: 'word', 'pdf', or 'excel'
            filename: Optional custom filename
            
        Returns:
            Dict with success status and file path
        """
        try:
            print(f"🎯 Generating {format_type.upper()} report...")
            
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                report_type = report_data.get('metadata', {}).get('report_type', 'report')
                filename = f"payroll_audit_{report_type}_{timestamp}.{self._get_file_extension(format_type)}"
            
            file_path = os.path.join(self.output_dir, filename)
            
            # Generate based on format
            if format_type.lower() == 'word':
                result = self._generate_word_report(report_data, file_path)
            elif format_type.lower() == 'pdf':
                result = self._generate_pdf_report(report_data, file_path)
            elif format_type.lower() == 'excel':
                result = self._generate_excel_report(report_data, file_path)
            else:
                raise ValueError(f"Unsupported format: {format_type}")
            
            if result['success']:
                print(f"✅ {format_type.upper()} report generated: {file_path}")
            
            return result
            
        except Exception as e:
            print(f"❌ Failed to generate {format_type} report: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_file_extension(self, format_type: str) -> str:
        """Get file extension for format type"""
        extensions = {
            'word': 'docx',
            'pdf': 'pdf',
            'excel': 'xlsx'
        }
        return extensions.get(format_type.lower(), 'txt')
    
    def _generate_word_report(self, report_data: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """Generate WORD document with Cambria font"""
        
        if not DOCX_AVAILABLE:
            return {
                'success': False,
                'error': 'python-docx library not available. Install with: pip install python-docx'
            }
        
        try:
            doc = Document()
            
            # Set document fonts and styles
            self._setup_word_styles(doc)
            
            # Add header
            self._add_word_header(doc, report_data)
            
            # Add executive summary
            self._add_word_executive_summary(doc, report_data)
            
            # Add findings
            self._add_word_findings(doc, report_data)
            
            # Add appendix if present
            if 'appendix' in report_data.get('report', {}):
                self._add_word_appendix(doc, report_data)
            
            # Save document
            doc.save(file_path)
            
            return {
                'success': True,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Word generation failed: {str(e)}"
            }
    
    def _setup_word_styles(self, doc):
        """Setup Word document styles"""
        # This would set up custom styles for Cambria body and Calibri headings
        # For now, we'll use default styles and modify them
        pass
    
    def _add_word_header(self, doc, report_data: Dict[str, Any]):
        """Add header section to Word document"""
        report = report_data.get('report', {})
        header = report.get('header', {})
        
        # Title
        title = doc.add_heading(header.get('title', 'PAYROLL AUDIT REPORT'), 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Report Information Table
        info = header.get('report_information', {})
        table = doc.add_table(rows=4, cols=2)
        table.style = 'Table Grid'
        
        table.cell(0, 0).text = 'Report Information'
        table.cell(0, 1).text = 'Executive Summary'
        
        table.cell(1, 0).text = f"Period: {info.get('period', 'N/A')}"
        table.cell(2, 0).text = f"Generated at: {info.get('generated_at', 'N/A')}"
        table.cell(3, 0).text = f"Generated By: {info.get('generated_by', 'N/A')}"
        
        # Executive Summary
        exec_summary = header.get('executive_summary', {})
        summary_text = f"Significant Changes Detected: {exec_summary.get('significant_changes_detected', 0)}\n"
        summary_text += f"HIGH Priority Changes: {exec_summary.get('high_priority_changes', 0)}\n"
        summary_text += f"MODERATE Priority Changes: {exec_summary.get('moderate_priority_changes', 0)}\n"
        summary_text += f"LOW Priority Changes: {exec_summary.get('low_priority_changes', 0)}"
        
        table.cell(1, 1).text = summary_text
        
        doc.add_page_break()
    
    def _add_word_executive_summary(self, doc, report_data: Dict[str, Any]):
        """Add executive summary section"""
        doc.add_heading('Executive Summary', level=1)
        
        # Add summary content based on report type
        report = report_data.get('report', {})
        if 'findings_and_observations' in report:
            self._add_employee_based_summary(doc, report)
        elif 'findings_by_item' in report:
            self._add_item_based_summary(doc, report)
    
    def _add_employee_based_summary(self, doc, report: Dict[str, Any]):
        """Add employee-based summary"""
        findings = report.get('findings_and_observations', {})
        
        doc.add_paragraph(f"This report covers {len(findings)} employees with payroll changes.")
        
        if report.get('new_employees'):
            doc.add_paragraph(f"New employees added: {len(report['new_employees'])}")
        
        if report.get('removed_employees'):
            doc.add_paragraph(f"Employees removed: {len(report['removed_employees'])}")
    
    def _add_item_based_summary(self, doc, report: Dict[str, Any]):
        """Add item-based summary"""
        findings = report.get('findings_by_item', {})
        total_items = sum(len(section_items) for section_items in findings.values())
        
        doc.add_paragraph(f"This report covers {total_items} payroll items across {len(findings)} sections.")
        
        if report.get('loan_changes'):
            doc.add_paragraph(f"Loan changes detected: {len(report['loan_changes'])}")
    
    def _add_word_findings(self, doc, report_data: Dict[str, Any]):
        """Add findings section"""
        doc.add_heading('Findings and Observations', level=1)
        
        report = report_data.get('report', {})
        
        if 'findings_and_observations' in report:
            self._add_word_employee_findings(doc, report['findings_and_observations'])
        elif 'findings_by_item' in report:
            self._add_word_item_findings(doc, report['findings_by_item'])
    
    def _add_word_employee_findings(self, doc, findings: Dict[str, Any]):
        """Add employee-based findings"""
        for emp_id, emp_data in findings.items():
            emp_info = emp_data.get('employee_info', {})
            
            # Employee header
            doc.add_heading(f"{emp_id}: {emp_info.get('name', 'Unknown')}", level=2)
            doc.add_paragraph(f"Department: {emp_info.get('department', 'N/A')}")
            
            # Changes
            for change in emp_data.get('changes', []):
                p = doc.add_paragraph()
                p.add_run(f"{change['item']}: ").bold = True
                p.add_run(change['description'])
                p.add_run(f" (Priority: {change['priority']})")
    
    def _add_word_item_findings(self, doc, findings: Dict[str, Any]):
        """Add item-based findings"""
        for section_name, section_items in findings.items():
            doc.add_heading(section_name, level=2)
            
            for item_label, item_data in section_items.items():
                doc.add_heading(item_label, level=3)
                
                item_info = item_data.get('item_info', {})
                doc.add_paragraph(f"Total employees affected: {item_info.get('total_employees_affected', 0)}")
                
                for change_group in item_data.get('employee_changes', []):
                    doc.add_paragraph(f"{change_group['change_type']} ({change_group['employee_count']} employees):")
                    
                    for employee in change_group['employees'][:5]:  # Limit to first 5
                        doc.add_paragraph(f"  • {employee['employee_name']}: {employee['change_description']}")
    
    def _add_word_appendix(self, doc, report_data: Dict[str, Any]):
        """Add appendix section"""
        doc.add_page_break()
        doc.add_heading('Appendix', level=1)
        
        appendix = report_data.get('report', {}).get('appendix', {})
        
        for section_name, section_data in appendix.items():
            doc.add_heading(section_name.replace('_', ' ').title(), level=2)
            
            for item in section_data:
                doc.add_paragraph(f"• {item}")
    
    def _generate_pdf_report(self, report_data: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """Generate PDF document"""
        
        if not PDF_AVAILABLE:
            return {
                'success': False,
                'error': 'reportlab library not available. Install with: pip install reportlab'
            }
        
        try:
            # Create PDF document
            doc = SimpleDocTemplate(file_path, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # Add content
            self._add_pdf_content(story, styles, report_data)
            
            # Build PDF
            doc.build(story)
            
            return {
                'success': True,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"PDF generation failed: {str(e)}"
            }
    
    def _add_pdf_content(self, story, styles, report_data: Dict[str, Any]):
        """Add content to PDF story"""
        report = report_data.get('report', {})
        header = report.get('header', {})
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # Center
        )
        
        story.append(Paragraph(header.get('title', 'PAYROLL AUDIT REPORT'), title_style))
        story.append(Spacer(1, 20))
        
        # Report Information
        info = header.get('report_information', {})
        story.append(Paragraph(f"<b>Period:</b> {info.get('period', 'N/A')}", styles['Normal']))
        story.append(Paragraph(f"<b>Generated By:</b> {info.get('generated_by', 'N/A')}", styles['Normal']))
        story.append(Paragraph(f"<b>Designation:</b> {info.get('designation', 'N/A')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Executive Summary
        exec_summary = header.get('executive_summary', {})
        story.append(Paragraph('<b>Executive Summary</b>', styles['Heading2']))
        story.append(Paragraph(f"Significant Changes: {exec_summary.get('significant_changes_detected', 0)}", styles['Normal']))
        story.append(Paragraph(f"High Priority: {exec_summary.get('high_priority_changes', 0)}", styles['Normal']))
        
        # Add more content based on report type...
    
    def _generate_excel_report(self, report_data: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """Generate EXCEL spreadsheet"""
        
        if not EXCEL_AVAILABLE:
            return {
                'success': False,
                'error': 'openpyxl library not available. Install with: pip install openpyxl'
            }
        
        try:
            # Create workbook
            wb = openpyxl.Workbook()
            
            # Remove default sheet
            wb.remove(wb.active)
            
            # Add sheets based on report type
            report = report_data.get('report', {})
            
            if 'findings_and_observations' in report:
                self._add_excel_employee_sheets(wb, report)
            elif 'findings_by_item' in report:
                self._add_excel_item_sheets(wb, report)
            
            # Add summary sheet
            self._add_excel_summary_sheet(wb, report_data)
            
            # Save workbook
            wb.save(file_path)
            
            return {
                'success': True,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Excel generation failed: {str(e)}"
            }
    
    def _add_excel_employee_sheets(self, wb, report: Dict[str, Any]):
        """Add employee-based sheets to Excel"""
        
        # Employee Changes sheet
        ws = wb.create_sheet("Employee Changes")
        
        # Headers
        headers = ['Employee ID', 'Employee Name', 'Department', 'Section', 'Item', 'Description', 'Priority']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # Data
        row = 2
        findings = report.get('findings_and_observations', {})
        for emp_id, emp_data in findings.items():
            emp_info = emp_data.get('employee_info', {})
            
            for change in emp_data.get('changes', []):
                ws.cell(row=row, column=1, value=emp_info.get('id', ''))
                ws.cell(row=row, column=2, value=emp_info.get('name', ''))
                ws.cell(row=row, column=3, value=emp_info.get('department', ''))
                ws.cell(row=row, column=4, value=change.get('section', ''))
                ws.cell(row=row, column=5, value=change.get('item', ''))
                ws.cell(row=row, column=6, value=change.get('description', ''))
                ws.cell(row=row, column=7, value=change.get('priority', ''))
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_excel_item_sheets(self, wb, report: Dict[str, Any]):
        """Add item-based sheets to Excel"""
        
        # Item Changes sheet
        ws = wb.create_sheet("Item Changes")
        
        # Headers
        headers = ['Section', 'Item', 'Change Type', 'Employee Count', 'Employee ID', 'Employee Name', 'Description']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # Data
        row = 2
        findings = report.get('findings_by_item', {})
        for section_name, section_items in findings.items():
            for item_label, item_data in section_items.items():
                for change_group in item_data.get('employee_changes', []):
                    for employee in change_group.get('employees', []):
                        ws.cell(row=row, column=1, value=section_name)
                        ws.cell(row=row, column=2, value=item_label)
                        ws.cell(row=row, column=3, value=change_group.get('change_type', ''))
                        ws.cell(row=row, column=4, value=change_group.get('employee_count', 0))
                        ws.cell(row=row, column=5, value=employee.get('employee_id', ''))
                        ws.cell(row=row, column=6, value=employee.get('employee_name', ''))
                        ws.cell(row=row, column=7, value=employee.get('change_description', ''))
                        row += 1
    
    def _add_excel_summary_sheet(self, wb, report_data: Dict[str, Any]):
        """Add summary sheet to Excel"""
        
        ws = wb.create_sheet("Summary", 0)  # Insert at beginning
        
        # Title
        ws.cell(row=1, column=1, value="PAYROLL AUDIT REPORT SUMMARY").font = Font(size=16, bold=True)
        
        # Report Information
        report = report_data.get('report', {})
        header = report.get('header', {})
        info = header.get('report_information', {})
        
        ws.cell(row=3, column=1, value="Report Information").font = Font(bold=True)
        ws.cell(row=4, column=1, value="Period:")
        ws.cell(row=4, column=2, value=info.get('period', 'N/A'))
        ws.cell(row=5, column=1, value="Generated By:")
        ws.cell(row=5, column=2, value=info.get('generated_by', 'N/A'))
        ws.cell(row=6, column=1, value="Designation:")
        ws.cell(row=6, column=2, value=info.get('designation', 'N/A'))
        
        # Executive Summary
        exec_summary = header.get('executive_summary', {})
        ws.cell(row=8, column=1, value="Executive Summary").font = Font(bold=True)
        ws.cell(row=9, column=1, value="Total Changes:")
        ws.cell(row=9, column=2, value=exec_summary.get('significant_changes_detected', 0))
        ws.cell(row=10, column=1, value="High Priority:")
        ws.cell(row=10, column=2, value=exec_summary.get('high_priority_changes', 0))

def main():
    """Test the report format manager"""
    
    # Mock report data
    mock_data = {
        'report': {
            'header': {
                'title': 'PAYROLL AUDIT REPORT: JULY 2025',
                'report_information': {
                    'period': 'July 2025',
                    'generated_by': 'SAMUEL ASIEDU',
                    'designation': 'PAYROLL AUDITOR'
                },
                'executive_summary': {
                    'significant_changes_detected': 4,
                    'high_priority_changes': 3,
                    'moderate_priority_changes': 1,
                    'low_priority_changes': 0
                }
            }
        },
        'metadata': {
            'report_type': 'employee-based'
        }
    }
    
    manager = ReportFormatManager()
    
    # Test each format
    for format_type in ['word', 'pdf', 'excel']:
        result = manager.generate_report(mock_data, format_type)
        if result['success']:
            print(f"✅ {format_type.upper()} test passed")
        else:
            print(f"❌ {format_type.upper()} test failed: {result['error']}")

if __name__ == "__main__":
    main()
