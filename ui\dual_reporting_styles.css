/* DUAL REPORTING SYSTEM STYLES */

/* Main Dual Reporting Container */
.dual-reporting-header {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 25px;
  border-radius: 12px 12px 0 0;
  text-align: center;
  margin-bottom: 0;
}

.dual-reporting-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.dual-reporting-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

/* Dual Report Configuration Section */
.dual-config-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.dual-config-section .config-header {
  margin-bottom: 15px;
}

.dual-config-section .config-header h3 {
  margin: 0 0 5px 0;
  color: #495057;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dual-config-section .config-header p {
  margin: 0;
  color: #6c757d;
  font-size: 13px;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.config-field {
  display: flex;
  flex-direction: column;
}

.config-field label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
  font-size: 14px;
}

/* Toggle Section */
.toggle-section {
  margin: 25px 0;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.toggle-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.toggle-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 180px;
  text-align: center;
}

.toggle-option.active {
  background: #e3f2fd;
  border: 2px solid #2196f3;
}

.toggle-option i {
  font-size: 24px;
  margin-bottom: 8px;
  color: #495057;
}

.toggle-option.active i {
  color: #2196f3;
}

.toggle-option span {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
  color: #495057;
}

.toggle-option.active span {
  color: #2196f3;
}

.toggle-option small {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.3;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
}

.toggle-input {
  display: none;
}

.toggle-label {
  display: block;
  width: 60px;
  height: 30px;
  background: #ccc;
  border-radius: 15px;
  cursor: pointer;
  position: relative;
  transition: background 0.3s ease;
}

.toggle-input:checked + .toggle-label {
  background: #4caf50;
}

.toggle-slider {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-input:checked + .toggle-label .toggle-slider {
  transform: translateX(30px);
}

/* Dual Reporting Main Container */
.dual-reporting-main {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
  margin: 20px 0;
}

/* Reporting Sides */
.reporting-side {
  display: none;
  padding: 25px;
}

.reporting-side.active {
  display: block;
}

.side-header {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.side-header h3 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.side-header p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

/* Controls Grid */
.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.control-group {
  display: flex;
  flex-direction: column;
}

.control-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
  font-size: 14px;
}

/* Pre-Reporting Specific Styles */
.pre-reporting-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.pre-reporting-actions {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

/* Smart Reporter Specific Styles */
.smart-reporter-controls {
  background: #fff3e0;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.smart-reporter-actions {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

/* Business Rules Pane */
.business-rules-pane {
  background: #f1f8e9;
  border: 1px solid #c8e6c9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.rules-header h4 {
  margin: 0;
  color: #2e7d32;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rules-actions {
  display: flex;
  gap: 10px;
}

.rules-list {
  min-height: 100px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
}

/* Business Rule Items */
.business-rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.business-rule-item.active {
  border-color: #4caf50;
  background: #f1f8e9;
}

.business-rule-item.inactive {
  border-color: #f44336;
  background: #ffebee;
  opacity: 0.7;
}

.rule-info {
  flex: 1;
}

.rule-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.rule-description {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.rule-type {
  font-size: 11px;
  color: #999;
  text-transform: uppercase;
  font-weight: 500;
}

.rule-actions {
  display: flex;
  gap: 8px;
}

.btn-toggle-rule {
  padding: 4px 8px;
  font-size: 11px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-enable {
  background: #4caf50;
  color: white;
}

.btn-disable {
  background: #f44336;
  color: white;
}

.btn-toggle-rule:hover {
  opacity: 0.8;
}

.no-rules {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

/* Smart Reporter Specific Styles */
.rules-status {
  margin-bottom: 20px;
}

.rules-status h5 {
  margin: 0 0 10px 0;
  color: #2e7d32;
  font-size: 14px;
  font-weight: 600;
}

.rules-summary {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.rule-status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #333;
}

.rule-status-item i {
  color: #4caf50;
  font-size: 10px;
}

.detection-results {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.detection-results h5 {
  margin: 0 0 10px 0;
  color: #1976d2;
  font-size: 14px;
  font-weight: 600;
}

.detection-stats {
  display: flex;
  gap: 15px;
}

.detection-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 6px;
  min-width: 60px;
}

.detection-count {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 2px;
}

.detection-label {
  font-size: 10px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
}

/* Business Rules Engine Styles */
.rule-category {
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.category-title {
  background: #f5f5f5;
  padding: 12px 15px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.rule-count {
  background: #2196f3;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  margin-left: auto;
}

.category-rules {
  padding: 10px;
}

.rule-item {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
  padding: 12px;
  transition: all 0.3s ease;
}

.rule-item.active {
  border-color: #4caf50;
  background: #f8fff8;
}

.rule-item.inactive {
  border-color: #f44336;
  background: #fff8f8;
  opacity: 0.7;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rule-title {
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.strict-badge {
  background: #ff9800;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
}

.rule-actions {
  display: flex;
  gap: 6px;
}

.btn-toggle-rule,
.btn-edit-rule {
  padding: 4px 8px;
  font-size: 11px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-enable {
  background: #4caf50;
  color: white;
}

.btn-disable {
  background: #f44336;
  color: white;
}

.btn-edit-rule {
  background: #2196f3;
  color: white;
}

.btn-toggle-rule:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-toggle-rule:hover:not(:disabled),
.btn-edit-rule:hover {
  opacity: 0.8;
}

.rule-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  font-style: italic;
}

/* Navigation */
.dual-reporting-navigation {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

/* Button Styles */
.btn.large {
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
}

.btn.small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn.success {
  background: #4caf50;
  color: white;
  border: none;
}

.btn.success:hover {
  background: #45a049;
}

/* Form Input Styles */
.form-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* Loading Container */
.loading-container {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Pre-Reporting Styles */
.pre-reporting-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2196f3;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
}

/* Employee Groups */
.employee-group {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.employee-header {
  background: #f5f5f5;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.employee-info h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 16px;
}

.employee-id {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.change-count {
  background: #2196f3;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

/* Change Items */
.employee-changes {
  padding: 10px;
}

.change-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.change-item:hover {
  background: #f0f0f0;
  border-color: #ccc;
}

.change-item.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.change-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}

.change-main {
  flex: 1;
}

.change-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.change-section {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
}

.change-values {
  flex: 1;
  text-align: center;
}

.value-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 4px;
}

.previous-value,
.current-value {
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 13px;
}

.previous-value {
  background: #ffebee;
  color: #d32f2f;
}

.current-value {
  background: #e8f5e8;
  color: #388e3c;
}

.value-change i {
  color: #666;
  font-size: 12px;
}

.change-type {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 2px 6px;
  border-radius: 4px;
}

.change-type.increased {
  background: #c8e6c9;
  color: #2e7d32;
}

.change-type.decreased {
  background: #ffcdd2;
  color: #c62828;
}

.change-type.new {
  background: #bbdefb;
  color: #1565c0;
}

.change-type.removed {
  background: #f8bbd9;
  color: #ad1457;
}

.change-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

.priority-badge,
.bulk-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  text-transform: uppercase;
}

.priority-badge.high {
  background: #ffcdd2;
  color: #c62828;
}

.priority-badge.moderate {
  background: #fff3e0;
  color: #ef6c00;
}

.priority-badge.low {
  background: #e8f5e8;
  color: #2e7d32;
}

.bulk-badge {
  background: #f3e5f5;
  color: #7b1fa2;
}

.change-actions {
  margin-left: 15px;
}

.change-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* Message States */
.no-data-message,
.error-message {
  text-align: center;
  padding: 40px;
  color: #666;
}

.no-data-message i,
.error-message i {
  font-size: 48px;
  margin-bottom: 15px;
  color: #ccc;
}

.error-message i {
  color: #f44336;
}

.no-data-message h3,
.error-message h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.no-data-message p,
.error-message p {
  margin: 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .config-grid,
  .controls-grid {
    grid-template-columns: 1fr;
  }

  .toggle-container {
    flex-direction: column;
    gap: 15px;
  }

  .toggle-option {
    min-width: auto;
    width: 100%;
  }

  .summary-stats {
    flex-direction: column;
    gap: 10px;
  }

  .change-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .change-values {
    align-self: stretch;
  }

  .value-change {
    justify-content: flex-start;
  }
}
