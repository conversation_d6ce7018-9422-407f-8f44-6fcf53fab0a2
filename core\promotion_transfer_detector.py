#!/usr/bin/env python3
"""
PROMOTION AND TRANSFER DETECTION SYSTEM
Advanced algorithms to detect promotions and transfers, and generate appendix sections
"""

import os
import sys
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

class PromotionTransferDetector:
    """
    Advanced detection system for promotions and transfers with appendix generation
    """
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or './data/templar_payroll_auditor.db'
        self.detection_results = {
            'promotions': {
                'staff': [],
                'ministers': []
            },
            'transfers': {
                'staff': [],
                'ministers': []
            },
            'new_employees': [],
            'removed_employees': []
        }
        
    def detect_all_changes(self, session_id: str) -> Dict[str, Any]:
        """
        Detect all promotions, transfers, and employee changes for a session
        
        Args:
            session_id: Session identifier
            
        Returns:
            Dict containing all detection results
        """
        try:
            print(f"🔍 DETECTING PROMOTIONS AND TRANSFERS for session: {session_id}")
            
            # Load employee data
            employee_data = self._load_employee_data(session_id)
            
            if not employee_data:
                print("⚠️ No employee data found for detection")
                return self.detection_results
            
            print(f"📊 Analyzing {len(employee_data)} employees...")
            
            # Detect promotions
            self._detect_promotions(employee_data)
            
            # Detect transfers
            self._detect_transfers(employee_data)
            
            # Detect new employees
            self._detect_new_employees(employee_data)
            
            # Detect removed employees (requires separate query)
            self._detect_removed_employees(session_id)
            
            # Save detection results to database
            self._save_detection_results(session_id)
            
            # Generate appendix data
            appendix_data = self._generate_appendix_data()
            
            print(f"✅ DETECTION COMPLETE:")
            print(f"   Staff Promotions: {len(self.detection_results['promotions']['staff'])}")
            print(f"   Minister Promotions: {len(self.detection_results['promotions']['ministers'])}")
            print(f"   Staff Transfers: {len(self.detection_results['transfers']['staff'])}")
            print(f"   Minister Transfers: {len(self.detection_results['transfers']['ministers'])}")
            print(f"   New Employees: {len(self.detection_results['new_employees'])}")
            print(f"   Removed Employees: {len(self.detection_results['removed_employees'])}")
            
            return {
                'success': True,
                'detection_results': self.detection_results,
                'appendix_data': appendix_data,
                'session_id': session_id,
                'detected_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Detection failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e)
            }
    
    def _load_employee_data(self, session_id: str) -> Dict[str, Any]:
        """Load employee data with changes for analysis"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Load comparison results grouped by employee
            query = '''
                SELECT cr.employee_id, cr.employee_name, e.department, cr.section_name,
                       cr.item_label, cr.previous_value, cr.current_value, cr.change_type,
                       cr.priority, e.job_title, e.section
                FROM comparison_results cr
                LEFT JOIN employees e ON cr.employee_id = e.employee_id AND e.session_id = cr.session_id
                WHERE cr.session_id = ?
                ORDER BY cr.employee_id, cr.section_name, cr.item_label
            '''
            
            cursor.execute(query, (session_id,))
            results = cursor.fetchall()
            
            # Group by employee
            employee_data = defaultdict(lambda: {
                'employee_id': '',
                'employee_name': '',
                'department': '',
                'job_title': '',
                'section': '',
                'changes': []
            })
            
            for row in results:
                emp_id = row[0]
                if not employee_data[emp_id]['employee_id']:
                    employee_data[emp_id]['employee_id'] = row[0]
                    employee_data[emp_id]['employee_name'] = row[1]
                    employee_data[emp_id]['department'] = row[2] or ''
                    employee_data[emp_id]['job_title'] = row[9] or ''
                    employee_data[emp_id]['section'] = row[10] or ''
                
                change = {
                    'section_name': row[3],
                    'item_label': row[4],
                    'previous_value': row[5],
                    'current_value': row[6],
                    'change_type': row[7],
                    'priority': row[8]
                }
                
                employee_data[emp_id]['changes'].append(change)
            
            conn.close()
            
            print(f"✅ Loaded data for {len(employee_data)} employees")
            return dict(employee_data)
            
        except Exception as e:
            print(f"❌ Failed to load employee data: {str(e)}")
            return {}
    
    def _detect_promotions(self, employee_data: Dict[str, Any]):
        """Detect promotions based on business rules"""
        print("🎯 Detecting promotions...")
        
        for emp_id, emp_info in employee_data.items():
            changes = emp_info['changes']
            
            # Check for basic salary increase
            has_salary_increase = any(
                change['item_label'] in ['BASIC_SALARY', 'BASIC SALARY'] and 
                change['change_type'] == 'INCREASED'
                for change in changes
            )
            
            # Check for job title change
            has_job_title_change = any(
                change['item_label'] in ['JOB_TITLE', 'JOB TITLE', 'DESIGNATION'] and 
                change['change_type'] == 'CHANGED'
                for change in changes
            )
            
            # Check if it's a minister
            is_minister = self._is_minister(emp_info)
            
            # Apply promotion detection rules
            if has_salary_increase and has_job_title_change and not is_minister:
                # Staff promotion detected
                promotion = {
                    'employee_id': emp_info['employee_id'],
                    'employee_name': emp_info['employee_name'],
                    'department': emp_info['department'],
                    'previous_title': self._get_previous_job_title(changes),
                    'current_title': self._get_current_job_title(changes),
                    'salary_increase': self._get_salary_increase(changes),
                    'detection_rule': 'Staff Promotion: Basic salary increase + Job title change'
                }
                self.detection_results['promotions']['staff'].append(promotion)
                
            elif has_job_title_change and is_minister:
                # Minister promotion detected
                promotion = {
                    'employee_id': emp_info['employee_id'],
                    'employee_name': emp_info['employee_name'],
                    'department': emp_info['department'],
                    'previous_title': self._get_previous_job_title(changes),
                    'current_title': self._get_current_job_title(changes),
                    'detection_rule': 'Minister Promotion: Job title change for Ministers department'
                }
                self.detection_results['promotions']['ministers'].append(promotion)
    
    def _detect_transfers(self, employee_data: Dict[str, Any]):
        """Detect transfers based on business rules"""
        print("🔄 Detecting transfers...")
        
        for emp_id, emp_info in employee_data.items():
            changes = emp_info['changes']
            
            # Check for department change
            has_department_change = any(
                change['item_label'] in ['DEPARTMENT', 'DEPT'] and 
                change['change_type'] == 'CHANGED'
                for change in changes
            )
            
            # Check for section change (alternative indicator)
            has_section_change = any(
                change['item_label'] in ['SECTION', 'UNIT'] and 
                change['change_type'] == 'CHANGED'
                for change in changes
            )
            
            if has_department_change or has_section_change:
                is_minister = self._is_minister(emp_info)
                
                transfer = {
                    'employee_id': emp_info['employee_id'],
                    'employee_name': emp_info['employee_name'],
                    'previous_department': self._get_previous_department(changes),
                    'current_department': self._get_current_department(changes),
                    'previous_section': self._get_previous_section(changes),
                    'current_section': self._get_current_section(changes),
                    'detection_rule': f"{'Minister' if is_minister else 'Staff'} Transfer: Department/Section change"
                }
                
                if is_minister:
                    self.detection_results['transfers']['ministers'].append(transfer)
                else:
                    self.detection_results['transfers']['staff'].append(transfer)
    
    def _detect_new_employees(self, employee_data: Dict[str, Any]):
        """Detect new employees"""
        print("👤 Detecting new employees...")
        
        for emp_id, emp_info in employee_data.items():
            changes = emp_info['changes']
            
            # Check if this is a new employee (multiple NEW changes in personal details)
            new_personal_changes = [
                change for change in changes 
                if change['section_name'] == 'PERSONAL_DETAILS' and change['change_type'] == 'NEW'
            ]
            
            if len(new_personal_changes) >= 2:  # Multiple new personal details indicate new employee
                new_employee = {
                    'employee_id': emp_info['employee_id'],
                    'employee_name': emp_info['employee_name'],
                    'department': emp_info['department'],
                    'job_title': emp_info['job_title'],
                    'section': emp_info['section'],
                    'new_items': [change['item_label'] for change in new_personal_changes]
                }
                self.detection_results['new_employees'].append(new_employee)
    
    def _detect_removed_employees(self, session_id: str):
        """Detect removed employees (present in previous but not current)"""
        print("👥 Detecting removed employees...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Find employees in previous session but not in current
            # This would require a more complex query comparing sessions
            # For now, we'll look for REMOVED changes in personal details
            
            query = '''
                SELECT DISTINCT cr.employee_id, cr.employee_name, e.department
                FROM comparison_results cr
                LEFT JOIN employees e ON cr.employee_id = e.employee_id AND e.session_id = cr.session_id
                WHERE cr.session_id = ?
                AND cr.section_name = 'PERSONAL_DETAILS'
                AND cr.change_type = 'REMOVED'
                AND cr.item_label IN ('EMPLOYEE_NAME', 'EMPLOYEE_NO')
            '''
            
            cursor.execute(query, (session_id,))
            results = cursor.fetchall()
            
            for row in results:
                removed_employee = {
                    'employee_id': row[0],
                    'employee_name': row[1],
                    'department': row[2] or 'Unknown'
                }
                self.detection_results['removed_employees'].append(removed_employee)
            
            conn.close()
            
        except Exception as e:
            print(f"⚠️ Failed to detect removed employees: {str(e)}")
    
    def _is_minister(self, emp_info: Dict[str, Any]) -> bool:
        """Check if employee is a minister"""
        department = (emp_info.get('department') or '').upper()
        job_title = (emp_info.get('job_title') or '').upper()
        
        minister_keywords = ['MINISTER', 'MINISTERS', 'PASTORAL', 'CLERGY']
        
        return any(keyword in department for keyword in minister_keywords) or \
               any(keyword in job_title for keyword in minister_keywords)
    
    def _get_previous_job_title(self, changes: List[Dict]) -> str:
        """Get previous job title from changes"""
        for change in changes:
            if change['item_label'] in ['JOB_TITLE', 'JOB TITLE', 'DESIGNATION']:
                return change['previous_value'] or 'Unknown'
        return 'Unknown'
    
    def _get_current_job_title(self, changes: List[Dict]) -> str:
        """Get current job title from changes"""
        for change in changes:
            if change['item_label'] in ['JOB_TITLE', 'JOB TITLE', 'DESIGNATION']:
                return change['current_value'] or 'Unknown'
        return 'Unknown'
    
    def _get_salary_increase(self, changes: List[Dict]) -> Dict[str, Any]:
        """Get salary increase details"""
        for change in changes:
            if change['item_label'] in ['BASIC_SALARY', 'BASIC SALARY']:
                try:
                    prev_val = float(change['previous_value'] or 0)
                    curr_val = float(change['current_value'] or 0)
                    increase = curr_val - prev_val
                    percentage = (increase / prev_val * 100) if prev_val > 0 else 0
                    
                    return {
                        'previous_amount': prev_val,
                        'current_amount': curr_val,
                        'increase_amount': increase,
                        'increase_percentage': round(percentage, 2)
                    }
                except (ValueError, TypeError):
                    pass
        
        return {'previous_amount': 0, 'current_amount': 0, 'increase_amount': 0, 'increase_percentage': 0}
    
    def _get_previous_department(self, changes: List[Dict]) -> str:
        """Get previous department from changes"""
        for change in changes:
            if change['item_label'] in ['DEPARTMENT', 'DEPT']:
                return change['previous_value'] or 'Unknown'
        return 'Unknown'
    
    def _get_current_department(self, changes: List[Dict]) -> str:
        """Get current department from changes"""
        for change in changes:
            if change['item_label'] in ['DEPARTMENT', 'DEPT']:
                return change['current_value'] or 'Unknown'
        return 'Unknown'
    
    def _get_previous_section(self, changes: List[Dict]) -> str:
        """Get previous section from changes"""
        for change in changes:
            if change['item_label'] in ['SECTION', 'UNIT']:
                return change['previous_value'] or 'Unknown'
        return 'Unknown'
    
    def _get_current_section(self, changes: List[Dict]) -> str:
        """Get current section from changes"""
        for change in changes:
            if change['item_label'] in ['SECTION', 'UNIT']:
                return change['current_value'] or 'Unknown'
        return 'Unknown'
    
    def _save_detection_results(self, session_id: str):
        """Save detection results to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Clear existing detection results for this session
            cursor.execute("DELETE FROM promotion_transfer_detection WHERE session_id = ?", (session_id,))
            
            # Save all detection results
            all_detections = []
            
            # Staff promotions
            for promotion in self.detection_results['promotions']['staff']:
                all_detections.append((
                    session_id, promotion['employee_id'], promotion['employee_name'],
                    'PROMOTION_STAFF', promotion['previous_title'], promotion['current_title'],
                    promotion['department'], json.dumps(promotion)
                ))
            
            # Minister promotions
            for promotion in self.detection_results['promotions']['ministers']:
                all_detections.append((
                    session_id, promotion['employee_id'], promotion['employee_name'],
                    'PROMOTION_MINISTER', promotion['previous_title'], promotion['current_title'],
                    promotion['department'], json.dumps(promotion)
                ))
            
            # Staff transfers
            for transfer in self.detection_results['transfers']['staff']:
                all_detections.append((
                    session_id, transfer['employee_id'], transfer['employee_name'],
                    'TRANSFER_STAFF', transfer['previous_department'], transfer['current_department'],
                    transfer['current_department'], json.dumps(transfer)
                ))
            
            # Minister transfers
            for transfer in self.detection_results['transfers']['ministers']:
                all_detections.append((
                    session_id, transfer['employee_id'], transfer['employee_name'],
                    'TRANSFER_MINISTER', transfer['previous_department'], transfer['current_department'],
                    transfer['current_department'], json.dumps(transfer)
                ))
            
            # New employees
            for new_emp in self.detection_results['new_employees']:
                all_detections.append((
                    session_id, new_emp['employee_id'], new_emp['employee_name'],
                    'NEW_EMPLOYEE', '', new_emp['job_title'],
                    new_emp['department'], json.dumps(new_emp)
                ))
            
            # Insert all detections
            if all_detections:
                cursor.executemany("""
                    INSERT INTO promotion_transfer_detection (
                        session_id, employee_id, employee_name, detection_type,
                        previous_value, current_value, department, detection_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, all_detections)
            
            conn.commit()
            conn.close()
            
            print(f"✅ Saved {len(all_detections)} detection results to database")
            
        except Exception as e:
            print(f"⚠️ Failed to save detection results: {str(e)}")
    
    def _generate_appendix_data(self) -> Dict[str, List[str]]:
        """Generate appendix data for reports"""
        
        appendix = {}
        
        # Staff promotions appendix
        if self.detection_results['promotions']['staff']:
            appendix['PROMOTIONS STAFF'] = [
                f"{p['employee_id']}: {p['employee_name']} - {p['department']} "
                f"(From {p['previous_title']} to {p['current_title']})"
                for p in self.detection_results['promotions']['staff']
            ]
        
        # Minister promotions appendix
        if self.detection_results['promotions']['ministers']:
            appendix['PROMOTIONS MINISTERS'] = [
                f"{p['employee_id']}: {p['employee_name']} - {p['department']} "
                f"(From {p['previous_title']} to {p['current_title']})"
                for p in self.detection_results['promotions']['ministers']
            ]
        
        # Staff transfers appendix
        if self.detection_results['transfers']['staff']:
            appendix['TRANSFERS STAFF'] = [
                f"{t['employee_id']}: {t['employee_name']} "
                f"(From {t['previous_department']} to {t['current_department']})"
                for t in self.detection_results['transfers']['staff']
            ]
        
        # Minister transfers appendix
        if self.detection_results['transfers']['ministers']:
            appendix['TRANSFERS MINISTERS'] = [
                f"{t['employee_id']}: {t['employee_name']} "
                f"(From {t['previous_department']} to {t['current_department']})"
                for t in self.detection_results['transfers']['ministers']
            ]
        
        # New employees appendix
        if self.detection_results['new_employees']:
            appendix['NEW EMPLOYEES'] = [
                f"{n['employee_id']}: {n['employee_name']} - {n['department']} ({n['job_title']})"
                for n in self.detection_results['new_employees']
            ]
        
        # Removed employees appendix
        if self.detection_results['removed_employees']:
            appendix['REMOVED EMPLOYEES'] = [
                f"{r['employee_id']}: {r['employee_name']} - {r['department']}"
                for r in self.detection_results['removed_employees']
            ]
        
        return appendix

def main():
    """Command line interface for promotion/transfer detection"""
    
    if len(sys.argv) < 2:
        print("Usage: python promotion_transfer_detector.py <session_id>")
        sys.exit(1)
    
    session_id = sys.argv[1]
    
    detector = PromotionTransferDetector()
    result = detector.detect_all_changes(session_id)
    
    # Output result as JSON
    print(json.dumps(result, indent=2))
    
    # Exit with appropriate code
    sys.exit(0 if result.get('success', False) else 1)

if __name__ == "__main__":
    main()
