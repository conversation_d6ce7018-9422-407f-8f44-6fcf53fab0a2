/**
 * SMART REPORTER MODULE
 * AI-powered intelligent reporting system with business rules and advanced algorithms
 */

class SmartReporter {
    constructor() {
        this.reportType = 'employee-based'; // 'employee-based' or 'item-based'
        this.reportFormat = 'word'; // 'word', 'pdf', 'excel'
        this.sourceData = [];
        this.processedData = [];
        this.businessRules = [];
        this.promotions = [];
        this.transfers = [];
        this.isInitialized = false;
        
        console.log('🧠 SmartReporter initialized');
    }

    /**
     * Initialize the Smart Reporter system
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ SmartReporter already initialized');
            return;
        }

        try {
            console.log('🚀 Initializing Smart Reporter...');
            
            // Load source data from pre-reporting
            await this.loadSourceData();
            
            // Load business rules
            await this.loadBusinessRules();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Process data with business rules
            await this.processDataWithRules();
            
            // Render initial interface
            this.renderSmartReporterInterface();
            
            this.isInitialized = true;
            console.log('✅ Smart Reporter initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Smart Reporter:', error);
            this.showError('Failed to initialize Smart Reporter: ' + error.message);
        }
    }

    /**
     * Load source data from pre-reporting system
     */
    async loadSourceData() {
        try {
            console.log('📊 Loading source data for Smart Reporter...');
            
            // Get data from pre-reporting enhanced or API
            if (window.preReportingEnhanced && window.preReportingEnhanced.rawData) {
                this.sourceData = [...window.preReportingEnhanced.rawData];
            } else if (window.api && window.api.getLatestPreReportingData) {
                const response = await window.api.getLatestPreReportingData();
                this.sourceData = response || [];
            } else {
                // Use mock data for testing
                this.sourceData = this.generateMockData();
            }
            
            console.log(`✅ Loaded ${this.sourceData.length} records for Smart Reporter`);
            
        } catch (error) {
            console.error('❌ Failed to load source data:', error);
            this.sourceData = this.generateMockData();
        }
    }

    /**
     * Generate mock data for testing
     */
    generateMockData() {
        return [
            {
                id: 1,
                employee_id: 'COP0209',
                employee_name: 'RICK JAMES',
                department: 'ABUAKWA AREA-MINISTERS',
                section_name: 'PERSONAL_DETAILS',
                item_label: 'JOB_TITLE',
                previous_value: 'ASSISTANT MINISTER',
                current_value: 'SENIOR MINISTER',
                change_type: 'CHANGED',
                priority: 'High'
            },
            {
                id: 2,
                employee_id: 'COP0209',
                employee_name: 'RICK JAMES',
                department: 'ABUAKWA AREA-MINISTERS',
                section_name: 'EARNINGS',
                item_label: 'BASIC_SALARY',
                previous_value: '5000.00',
                current_value: '6000.00',
                change_type: 'INCREASED',
                priority: 'High'
            },
            {
                id: 3,
                employee_id: 'COP1010',
                employee_name: 'JAMES NARH',
                department: 'KASOA AREA',
                section_name: 'PERSONAL_DETAILS',
                item_label: 'DEPARTMENT',
                previous_value: 'FINANCE',
                current_value: 'KASOA AREA',
                change_type: 'CHANGED',
                priority: 'Moderate'
            },
            {
                id: 4,
                employee_id: 'COP2626',
                employee_name: 'SAMUEL ASIEDU',
                department: 'AUDIT, MONITORING & EVALUATION',
                section_name: 'ALLOWANCES',
                item_label: 'SCHOLARSHIP FUND',
                previous_value: '5.00',
                current_value: '20.00',
                change_type: 'INCREASED',
                priority: 'Moderate'
            }
        ];
    }

    /**
     * Load business rules
     */
    async loadBusinessRules() {
        try {
            // Initialize business rules engine if not already done
            if (!window.businessRulesEngine) {
                window.businessRulesEngine = new BusinessRulesEngine();
                await window.businessRulesEngine.initialize();
            }

            // Get active business rules from the engine
            if (window.businessRulesEngine && window.businessRulesEngine.rules) {
                this.businessRules = window.businessRulesEngine.rules.filter(rule => rule.isActive);
            } else {
                this.businessRules = [];
            }

            console.log(`✅ Loaded ${this.businessRules.length} active business rules`);

        } catch (error) {
            console.error('❌ Failed to load business rules:', error);
            this.businessRules = [];
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Report type selection
        const reportTypeSelect = document.getElementById('report-type-selection');
        if (reportTypeSelect) {
            reportTypeSelect.addEventListener('change', (e) => {
                this.handleReportTypeChange(e.target.value);
            });
        }

        // Report format selection
        const reportFormatSelect = document.getElementById('report-format-selection');
        if (reportFormatSelect) {
            reportFormatSelect.addEventListener('change', (e) => {
                this.handleReportFormatChange(e.target.value);
            });
        }

        // Generate Final Report button
        const generateBtn = document.getElementById('generate-final-report-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateFinalReport();
            });
        }
    }

    /**
     * Process data with business rules
     */
    async processDataWithRules() {
        console.log('🔍 Processing data with business rules...');

        // Reset processed data
        this.processedData = [...this.sourceData];
        this.promotions = [];
        this.transfers = [];

        // Apply business rules using the engine
        if (window.businessRulesEngine) {
            const ruleResults = window.businessRulesEngine.applyRules(this.sourceData);
            this.promotions = ruleResults.promotions || [];
            this.transfers = ruleResults.transfers || [];
            this.loanChanges = ruleResults.loanChanges || [];
        } else {
            // Fallback to manual detection
            await this.detectPromotions();
            await this.detectTransfers();
        }

        await this.classifyChanges();

        console.log(`✅ Processed ${this.processedData.length} records`);
        console.log(`🎯 Detected ${this.promotions.length} promotions`);
        console.log(`🔄 Detected ${this.transfers.length} transfers`);
    }

    /**
     * Detect promotions based on business rules
     */
    async detectPromotions() {
        const employeeGroups = this.groupByEmployee(this.sourceData);
        
        for (const [employeeId, changes] of Object.entries(employeeGroups)) {
            const employee = changes[0];
            
            // Check for staff promotion (Basic salary increase + Job title change)
            const hasBasicSalaryIncrease = changes.some(change => 
                change.item_label === 'BASIC_SALARY' && change.change_type === 'INCREASED'
            );
            
            const hasJobTitleChange = changes.some(change => 
                change.item_label === 'JOB_TITLE' && change.change_type === 'CHANGED'
            );
            
            const isMinister = employee.department && employee.department.includes('MINISTERS');
            
            if (hasBasicSalaryIncrease && hasJobTitleChange && !isMinister) {
                this.promotions.push({
                    type: 'STAFF',
                    employee_id: employeeId,
                    employee_name: employee.employee_name,
                    department: employee.department
                });
            } else if (hasJobTitleChange && isMinister) {
                this.promotions.push({
                    type: 'MINISTER',
                    employee_id: employeeId,
                    employee_name: employee.employee_name,
                    department: employee.department
                });
            }
        }
    }

    /**
     * Detect transfers based on business rules
     */
    async detectTransfers() {
        const employeeGroups = this.groupByEmployee(this.sourceData);
        
        for (const [employeeId, changes] of Object.entries(employeeGroups)) {
            const employee = changes[0];
            
            // Check for department change
            const hasDepartmentChange = changes.some(change => 
                change.item_label === 'DEPARTMENT' && change.change_type === 'CHANGED'
            );
            
            if (hasDepartmentChange) {
                const isMinister = employee.department && employee.department.includes('MINISTERS');
                
                this.transfers.push({
                    type: isMinister ? 'MINISTER' : 'STAFF',
                    employee_id: employeeId,
                    employee_name: employee.employee_name,
                    department: employee.department
                });
            }
        }
    }

    /**
     * Classify changes for reporting
     */
    async classifyChanges() {
        // Additional classification logic can be added here
        console.log('📊 Classifying changes for intelligent reporting...');
    }

    /**
     * Group data by employee
     */
    groupByEmployee(data) {
        return data.reduce((groups, change) => {
            const key = change.employee_id;
            if (!groups[key]) {
                groups[key] = [];
            }
            groups[key].push(change);
            return groups;
        }, {});
    }

    /**
     * Handle report type change
     */
    handleReportTypeChange(reportType) {
        console.log(`📋 Report type changed: ${reportType}`);
        this.reportType = reportType;
        this.renderReportPreview();
    }

    /**
     * Handle report format change
     */
    handleReportFormatChange(format) {
        console.log(`📄 Report format changed: ${format}`);
        this.reportFormat = format;
        this.updateFormatSpecificOptions();
    }

    /**
     * Render Smart Reporter interface
     */
    renderSmartReporterInterface() {
        // Update business rules display
        this.renderBusinessRulesStatus();
        
        // Render report preview
        this.renderReportPreview();
        
        // Update statistics
        this.updateStatistics();
    }

    /**
     * Render business rules status
     */
    renderBusinessRulesStatus() {
        const rulesList = document.getElementById('business-rules-list');
        if (!rulesList) return;

        if (this.businessRules.length === 0) {
            rulesList.innerHTML = '<p class="no-rules">No active business rules. Configure rules to enable intelligent reporting.</p>';
            return;
        }

        const rulesHtml = `
            <div class="rules-status">
                <h5>Active Rules (${this.businessRules.length})</h5>
                <div class="rules-summary">
                    ${this.businessRules.map(rule => `
                        <div class="rule-status-item">
                            <i class="fas fa-check-circle"></i>
                            <span>${rule.name}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <div class="detection-results">
                <h5>Detection Results</h5>
                <div class="detection-stats">
                    <div class="detection-item">
                        <span class="detection-count">${this.promotions.length}</span>
                        <span class="detection-label">Promotions</span>
                    </div>
                    <div class="detection-item">
                        <span class="detection-count">${this.transfers.length}</span>
                        <span class="detection-label">Transfers</span>
                    </div>
                </div>
            </div>
        `;

        rulesList.innerHTML = rulesHtml;
    }

    /**
     * Render report preview
     */
    renderReportPreview() {
        console.log(`🔍 Rendering ${this.reportType} report preview...`);
        
        // This will show a preview of how the report will look
        // Implementation will be expanded in the next phase
    }

    /**
     * Update format-specific options
     */
    updateFormatSpecificOptions() {
        console.log(`⚙️ Updating options for ${this.reportFormat} format`);
        
        // Format-specific configurations will be implemented
    }

    /**
     * Update statistics display
     */
    updateStatistics() {
        console.log('📊 Updating Smart Reporter statistics...');
        
        // Statistics display will be implemented
    }

    /**
     * Generate final report
     */
    async generateFinalReport() {
        try {
            console.log('🎯 Generating Final Report...');
            console.log(`📋 Report Type: ${this.reportType}`);
            console.log(`📄 Format: ${this.reportFormat}`);

            // Show loading state
            this.showGenerationProgress('Preparing report data...');

            // Get shared configuration
            const config = window.dualReportingManager ?
                window.dualReportingManager.sharedConfig :
                { generatedBy: 'System', designation: 'Auditor' };

            // Get current session ID
            const sessionId = await this.getCurrentSessionId();
            if (!sessionId) {
                throw new Error('No active session found');
            }

            // Prepare report generation request
            const reportRequest = {
                sessionId: sessionId,
                reportType: this.reportType,
                format: this.reportFormat,
                config: {
                    generatedBy: config.generatedBy,
                    designation: config.designation
                },
                businessRules: this.businessRules.map(rule => rule.id),
                includePromotions: true,
                includeTransfers: true,
                includeLoanChanges: true
            };

            console.log('📊 Report Request:', reportRequest);

            // Update progress
            this.showGenerationProgress('Generating report...');

            // Call backend to generate report
            const response = await window.api.generateSmartReport(reportRequest);

            if (!response.success) {
                throw new Error(response.error || 'Report generation failed');
            }

            // Update progress
            this.showGenerationProgress('Report generated successfully!');

            // Show success message with details
            const metadata = response.metadata || {};
            const successMessage = `Smart Final Report Generated Successfully!

Type: ${this.reportType}
Format: ${this.reportFormat.toUpperCase()}
File: ${response.filename || 'report file'}
Size: ${this.formatFileSize(response.fileSize || 0)}

Statistics:
• Total Changes: ${metadata.totalChanges || 0}
• Promotions: ${metadata.promotions || 0}
• Transfers: ${metadata.transfers || 0}
• Generated By: ${config.generatedBy}

The report has been saved to the reports folder.`;

            alert(successMessage);

            // Hide progress
            this.hideGenerationProgress();

            // Update report history
            this.updateReportHistory(response);

        } catch (error) {
            console.error('❌ Failed to generate final report:', error);
            this.hideGenerationProgress();
            alert('Failed to generate final report: ' + error.message);
        }
    }

    /**
     * Get current session ID
     */
    async getCurrentSessionId() {
        try {
            if (window.api && window.api.getCurrentSessionId) {
                return await window.api.getCurrentSessionId();
            }
            return null;
        } catch (error) {
            console.error('❌ Failed to get session ID:', error);
            return null;
        }
    }

    /**
     * Show report generation progress
     */
    showGenerationProgress(message) {
        const generateBtn = document.getElementById('generate-final-report-btn');
        if (generateBtn) {
            generateBtn.disabled = true;
            generateBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${message}`;
        }
    }

    /**
     * Hide report generation progress
     */
    hideGenerationProgress() {
        const generateBtn = document.getElementById('generate-final-report-btn');
        if (generateBtn) {
            generateBtn.disabled = false;
            generateBtn.innerHTML = `<i class="fas fa-magic"></i> Generate-FINAL REPORT`;
        }
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Update report history
     */
    updateReportHistory(response) {
        console.log('📝 Report generated and saved:', response.filePath);
        // Could add to a recent reports list in the UI
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('❌ SmartReporter Error:', message);
        // Error display implementation
    }
}

// Export for global access
window.SmartReporter = SmartReporter;

console.log('✅ SmartReporter module loaded');
